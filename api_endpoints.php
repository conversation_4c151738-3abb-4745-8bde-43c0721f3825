<?php
/**
 * API Endpoints
 * Handles all API routes and requests
 */
header('Content-Type: application/json');
require_once 'auth.php';
require_once 'silo_api.php';
require_once 'database.php';

$auth = new Auth();
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Handle different API endpoints
switch ($path) {
    case '/api/login':
        handleLogin();
        break;
    case '/api/logout':
        handleLogout();
        break;
    case '/api/import-data':
        handleImportData();
        break;
    case '/api/climate-data':
        handleClimateData();
        break;
    case '/api/users':
        handleUsers();
        break;
    case '/api/dashboard':
        handleDashboard();
        break;
    default:
        http_response_code(404);
        echo json_encode(['error' => 'Endpoint not found']);
}

function handleLogin() {
    global $auth;
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $username = $input['username'] ?? '';
    $password = $input['password'] ?? '';
    
    if ($auth->login($username, $password)) {
        echo json_encode([
            'success' => true,
            'user' => $auth->getCurrentUser()
        ]);
    } else {
        http_response_code(401);
        echo json_encode(['error' => 'Invalid credentials']);
    }
}

function handleLogout() {
    global $auth;
    $auth->logout();
    echo json_encode(['success' => true]);
}

function handleImportData() {
    global $auth;
    
    if (!$auth->isLoggedIn() || !$auth->hasPermission('import_data')) {
        http_response_code(403);
        echo json_encode(['error' => 'Permission denied']);
        return;
    }
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    try {
        $siloAPI = new SiloAPI();
        $data = $siloAPI->fetchData(
            $input['latitude'],
            $input['longitude'],
            $input['start_date'],
            $input['end_date'],
            $input['format'] ?? 'standard'
        );
        
        // Store data in database
        $db = new Database();
        $pdo = $db->getConnection();
        
        $stmt = $pdo->prepare(
            "INSERT INTO climate_data (date, location, latitude, longitude, temperature, rainfall, humidity, pressure) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
        );
        
        $location = "Lat: {$input['latitude']}, Lon: {$input['longitude']}";
        $imported = 0;
        
        foreach ($data as $record) {
            $temperature = ($record['temperature_max'] + $record['temperature_min']) / 2;
            $stmt->execute([
                $record['date'],
                $location,
                $input['latitude'],
                $input['longitude'],
                $temperature,
                $record['rainfall'],
                rand(50, 90), // Simulated humidity
                rand(1000, 1020) // Simulated pressure
            ]);
            $imported++;
        }
        
        // Log import
        $stmt = $pdo->prepare(
            "INSERT INTO data_imports (user_id, source, records_count, start_date, end_date, latitude, longitude, status) 
             VALUES (?, 'SILO', ?, ?, ?, ?, ?, 'completed')"
        );
        $stmt->execute([
            $auth->getCurrentUser()['id'],
            $imported,
            $input['start_date'],
            $input['end_date'],
            $input['latitude'],
            $input['longitude']
        ]);
        
        echo json_encode([
            'success' => true,
            'records_imported' => $imported
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function handleClimateData() {
    global $auth;
    
    if (!$auth->isLoggedIn()) {
        http_response_code(403);
        echo json_encode(['error' => 'Authentication required']);
        return;
    }
    
    $db = new Database();
    $pdo = $db->getConnection();
    
    $where = [];
    $params = [];
    
    // Apply filters
    if (isset($_GET['start_date'])) {
        $where[] = "date >= ?";
        $params[] = $_GET['start_date'];
    }
    
    if (isset($_GET['end_date'])) {
        $where[] = "date <= ?";
        $params[] = $_GET['end_date'];
    }
    
    if (isset($_GET['location'])) {
        $where[] = "location LIKE ?";
        $params[] = "%{$_GET['location']}%";
    }
    
    $whereClause = empty($where) ? '' : 'WHERE ' . implode(' AND ', $where);
    
    $stmt = $pdo->prepare(
        "SELECT * FROM climate_data {$whereClause} ORDER BY date DESC LIMIT 1000"
    );
    $stmt->execute($params);
    
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode($data);
}

function handleUsers() {
    global $auth;
    
    if (!$auth->isLoggedIn() || !$auth->hasPermission('manage_users')) {
        http_response_code(403);
        echo json_encode(['error' => 'Permission denied']);
        return;
    }
    
    $db = new Database();
    $pdo = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $stmt = $pdo->prepare("SELECT id, username, role, name, created_at FROM users");
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo json_encode($users);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $stmt = $pdo->prepare(
            "INSERT INTO users (username, password, role, name) VALUES (?, ?, ?, ?)"
        );
        
        $hashedPassword = password_hash($input['password'], PASSWORD_DEFAULT);
        
        try {
            $stmt->execute([
                $input['username'],
                $hashedPassword,
                $input['role'],
                $input['name'] ?? $input['username']
            ]);
            
            echo json_encode(['success' => true]);
        } catch (PDOException $e) {
            http_response_code(400);
            echo json_encode(['error' => 'Username already exists']);
        }
    }
}

function handleDashboard() {
    global $auth;
    
    if (!$auth->isLoggedIn()) {
        http_response_code(403);
        echo json_encode(['error' => 'Authentication required']);
        return;
    }
    
    $db = new Database();
    $pdo = $db->getConnection();
    
    // Get summary statistics
    $stats = [
        'total_records' => 0,
        'locations' => 0,
        'recent_imports' => []
    ];
    
    // Count total records
    $stmt = $pdo->query("SELECT COUNT(*) FROM climate_data");
    $stats['total_records'] = $stmt->fetchColumn();
    
    // Count distinct locations
    $stmt = $pdo->query("SELECT COUNT(DISTINCT location) FROM climate_data");
    $stats['locations'] = $stmt->fetchColumn();
    
    // Get recent imports
    $stmt = $pdo->query(
        "SELECT di.*, u.username FROM data_imports di 
         JOIN users u ON di.user_id = u.id 
         ORDER BY di.created_at DESC LIMIT 5"
    );
    $stats['recent_imports'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode($stats);
}