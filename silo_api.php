<?php
/**
 * SiloAPI Class
 * Handles communication with the SILO climate data API
 */
class SiloAPI {
    public function fetchData($latitude, $longitude, $startDate, $endDate, $format = 'standard') {
        $url = $this->buildApiUrl($latitude, $longitude, $startDate, $endDate, $format);
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 30,
                'user_agent' => 'Climate Data Management System'
            ]
        ]);
        
        $response = file_get_contents($url, false, $context);
        
        if ($response === false) {
            throw new Exception('Failed to fetch data from SILO API');
        }
        
        return $this->parseResponse($response);
    }
    
    private function parseResponse($response) {
        $lines = explode("\n", trim($response));
        $data = [];
        
        // Skip header lines (usually start with *)
        $dataStarted = false;
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            if (empty($line) || strpos($line, '*') === 0) {
                continue;
            }
            
            if (!$dataStarted && preg_match('/^\d{4}/', $line)) {
                $dataStarted = true;
            }
            
            if ($dataStarted) {
                $fields = preg_split('/\s+/', $line);
                
                if (count($fields) >= 8) {
                    $data[] = [
                        'date' => $this->formatDate($fields[0]),
                        'day_of_year' => intval($fields[1]),
                        'temperature_max' => floatval($fields[2]),
                        'temperature_min' => floatval($fields[3]),
                        'rainfall' => floatval($fields[4]),
                        'evaporation' => floatval($fields[5]),
                        'radiation' => floatval($fields[6]),
                        'vapour_pressure' => floatval($fields[7])
                    ];
                }
            }
        }
        
        return $data;
    }
    
    private function formatDate($dateString) {
        // Convert YYYYMMDD to YYYY-MM-DD
        if (strlen($dateString) === 8) {
            return substr($dateString, 0, 4) . '-' . 
                   substr($dateString, 4, 2) . '-' . 
                   substr($dateString, 6, 2);
        }
        return $dateString;
    }
    
    private function buildApiUrl($latitude, $longitude, $startDate, $endDate, $format) {
        // Implement URL building logic here
        return "https://www.longpaddock.qld.gov.au/cgi-bin/silo/DataDrillDataset.php?" .
               "lat=$latitude&lon=$longitude&start=$startDate&finish=$endDate&format=$format";
    }
}