<?php
// Simplified SILO Climate Data Application - FIXED VERSION
session_start();

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Generate CSRF token
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Configuration
$apiBaseUrl = "https://www.longpaddock.qld.gov.au/cgi-bin/silo/";
$apiUsername = "<EMAIL>";
$apiPassword = "apirequest";

// Variable mapping to SILO codes
$variableMapping = [
    'daily_rain' => 'R',
    'max_temp' => 'X',
    'min_temp' => 'N',
    'daily_solar' => 'S',
    'vp' => 'V',
    'vp_deficit' => 'D'
];

// Unit mapping for variables
$unitMapping = [
    'R' => 'mm',
    'X' => '°C',
    'N' => '°C',
    'S' => 'MJ/m²',
    'V' => 'hPa',
    'D' => 'hPa'
];

// Variable display names
$variableNames = [
    'daily_rain' => 'Daily Rainfall',
    'max_temp' => 'Maximum Temperature',
    'min_temp' => 'Minimum Temperature',
    'daily_solar' => 'Daily Solar Radiation',
    'vp' => 'Vapour Pressure',
    'vp_deficit' => 'Vapour Pressure Deficit'
];

/**
 * Database connection
 */
function getDbConnection()
{
    $host = 'localhost';
    $dbname = 'climate_data';
    $username = 'root';
    $password = '';

    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        error_log("Database connection error: " . $e->getMessage());
        return false;
    }
}

/**
 * Fetch data from SILO API
 */
function fetchFromAPI($endpoint, $params)
{
    global $apiBaseUrl;
    $url = $apiBaseUrl . $endpoint . "?" . http_build_query($params);

    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FAILONERROR => true
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if (curl_errno($ch)) {
        $error = 'API Error: ' . curl_error($ch);
        curl_close($ch);
        return ['error' => $error];
    }
    curl_close($ch);

    if ($httpCode !== 200) {
        return ['error' => "API returned HTTP $httpCode"];
    }

    $data = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        return ['error' => 'JSON parse error: ' . json_last_error_msg()];
    }

    return $data;
}

/**
 * Save climate data to database - FIXED VERSION
 */
function saveClimateData($latitude, $longitude, $variable, $climateData)
{
    $conn = getDbConnection();
    if (!$conn) {
        return "Database connection failed";
    }

    try {
        $conn->beginTransaction();

        // Insert location WITHOUT variable_code
        $stmtLoc = $conn->prepare("INSERT IGNORE INTO locations (latitude, longitude) 
                                  VALUES (:lat, :lon)");
        $stmtLoc->execute([
            ':lat' => $latitude,
            ':lon' => $longitude
        ]);

        // Get location ID
        $stmt = $conn->prepare("SELECT id FROM locations WHERE latitude = :lat AND longitude = :lon");
        $stmt->execute([':lat' => $latitude, ':lon' => $longitude]);
        $locationId = $stmt->fetchColumn();

        if (!$locationId) {
            throw new Exception("Failed to get location ID");
        }

        // Insert climate data WITH variable_code
        $stmtClimate = $conn->prepare("INSERT INTO climate_data 
                                     (location_id, date, value, variable_code) 
                                     VALUES (:location_id, :date, :value, :variable_code)
                                     ON DUPLICATE KEY UPDATE value = :value");

        foreach ($climateData as $date => $data) {
            $stmtClimate->execute([
                ':location_id' => $locationId,
                ':date' => $date,
                ':value' => $data['value'],
                ':variable_code' => $variable
            ]);
        }

        $conn->commit();
        return true;
    } catch (Exception $e) {
        $conn->rollBack();
        return "Database error: " . $e->getMessage();
    }
}

/**
 * Get saved climate data from database
 */
function getSavedClimateData()
{
    $conn = getDbConnection();
    if (!$conn) {
        return [];
    }

    try {
        $stmt = $conn->query("SELECT l.latitude, l.longitude, 
                             c.date, c.value, c.variable_code, c.created_at
                             FROM locations l
                             JOIN climate_data c ON l.id = c.location_id
                             ORDER BY c.created_at DESC, c.date ASC
                             LIMIT 100");

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        return [];
    }
}

// Initialize variables
$climateData = [];
$error = '';
$message = '';
$searchPerformed = false;
$apiUrl = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit'])) {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $error = 'Security validation failed. Please try again.';
    } else {
        // Get and validate inputs
        $latitude = filter_input(INPUT_POST, 'latitude', FILTER_VALIDATE_FLOAT);
        $longitude = filter_input(INPUT_POST, 'longitude', FILTER_VALIDATE_FLOAT);
        @$startDate = filter_input(INPUT_POST, 'start_date', FILTER_SANITIZE_STRING);
        @$endDate = filter_input(INPUT_POST, 'end_date', FILTER_SANITIZE_STRING);
        @$variable = filter_input(INPUT_POST, 'variable', FILTER_SANITIZE_STRING);

        // Validate inputs
        if ($latitude === false || $longitude === false) {
            $error = 'Invalid coordinates';
        } elseif (empty($startDate) || empty($endDate)) {
            $error = 'Date range required';
        } elseif (empty($variable) || !isset($variableMapping[$variable])) {
            $error = 'Invalid climate variable';
        } elseif ($latitude < -29.0 || $latitude > -10.0 || $longitude < 138.0 || $longitude > 155.0) {
            $error = 'Coordinates must be within Queensland boundaries';
        } else {
            // Prepare API parameters
            $variableCode = $variableMapping[$variable];
            $params = [
                'lat' => round($latitude * 20) / 20,
                'lon' => round($longitude * 20) / 20,
                'start' => str_replace('-', '', $startDate),
                'finish' => str_replace('-', '', $endDate),
                'format' => 'json',
                'comment' => $variableCode,
                'username' => $apiUsername,
                'password' => $apiPassword
            ];

            // Store the API URL for display
            $apiUrl = $apiBaseUrl . 'DataDrillDataset.php?' . http_build_query($params);

            // Fetch data from API
            $response = fetchFromAPI('DataDrillDataset.php', $params);

            if (isset($response['error'])) {
                $error = $response['error'];
            } else {
                // Process API response
                $items = isset($response['data']) ? $response['data'] : [];

                foreach ($items as $item) {
                    if (isset($item['date']) && isset($item['variables'])) {
                        $date = $item['date'];

                        foreach ($item['variables'] as $variableData) {
                            if (
                                isset($variableData['variable_code']) &&
                                $variableData['variable_code'] === $variable &&
                                isset($variableData['value'])
                            ) {
                                $climateData[$date] = [
                                    'value' => round($variableData['value'], 2),
                                    'unit' => $unitMapping[$variableMapping[$variable]],
                                    'source' => $variableData['source'] ?? null
                                ];
                                break;
                            }
                        }
                    }
                }

                if (!empty($climateData)) {
                    // Save to database
                    $saveResult = saveClimateData($latitude, $longitude, $variable, $climateData);
                    if ($saveResult === true) {
                        $message = "Data successfully fetched and saved to database!<br><br>";
                        $message .= "<strong>API URL used:</strong><br>";
                        $message .= "<div style='background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 10px; border: 1px solid #dee2e6;'>";
                        $message .= "<code style='word-break: break-all; font-size: 11px; color: #0066cc; line-height: 1.4;'>" . htmlspecialchars($apiUrl) . "</code>";
                        $message .= "</div>";
                        $message .= "<br><small><strong>Records processed:</strong> " . count($climateData) . "</small>";
                    } else {
                        $error = "Failed to save data: " . $saveResult;
                    }
                    $searchPerformed = true;
                } else {
                    $error = "No data found for the specified criteria.";
                }
            }
        }
    }
}

// Get saved data for display
$savedData = getSavedClimateData();
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Queensland Climate Data Explorer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px 0;
        }

        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }

        .table-responsive {
            max-height: 400px;
            overflow-y: auto;
        }

        .api-url-display {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            border: 1px solid #dee2e6;
            word-break: break-all;
        }

        .api-url-display code {
            font-size: 11px;
            color: #0066cc;
            line-height: 1.4;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1 class="text-center mb-4">Queensland Climate Data Explorer</h1>

        <!-- Search Form -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">Search Climate Data</h3>
            </div>
            <div class="card-body">
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
                <?php endif; ?>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-success"><?php echo $message; ?></div>
                <?php endif; ?>

                <form method="post" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                    <div class="mb-3">
                        <label for="qldStations" class="form-label">Select a Queensland Station:</label>
                        <select id="qldStations" class="form-select">
                            <option value="">-- Select Station --</option>
                        </select>
                        <div class="station-details mt-2 p-2 bg-light rounded" style="display: none;">
                            <small>
                                <strong>Selected Station:</strong> <span id="selectedStationName">N/A</span><br>
                                <strong>Coordinates:</strong> <span id="selectedStationCoords">N/A</span>
                            </small>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="latitude" class="form-label">Latitude (-29.0 to -10.0)</label>
                            <input type="number" id="latitude" name="latitude" class="form-control"
                                step="0.01" min="-29.0" max="-10.0" required
                                value="<?php echo isset($_POST['latitude']) ? htmlspecialchars($_POST['latitude']) : '-27.47'; ?>">
                        </div>
                        <div class="col-md-6">
                            <label for="longitude" class="form-label">Longitude (138.0 to 155.0)</label>
                            <input type="number" id="longitude" name="longitude" class="form-control"
                                step="0.01" min="138.0" max="155.0" required
                                value="<?php echo isset($_POST['longitude']) ? htmlspecialchars($_POST['longitude']) : '153.03'; ?>">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" id="start_date" name="start_date" class="form-control" required
                                value="<?php echo isset($_POST['start_date']) ? htmlspecialchars($_POST['start_date']) : date('Y-m-d', strtotime('-1 month')); ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" id="end_date" name="end_date" class="form-control" required
                                value="<?php echo isset($_POST['end_date']) ? htmlspecialchars($_POST['end_date']) : date('Y-m-d'); ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="variable" class="form-label">Climate Variable</label>
                            <select id="variable" name="variable" class="form-select" required>
                                <?php foreach ($variableMapping as $key => $code): ?>
                                    <option value="<?php echo $key; ?>"
                                        <?php echo (isset($_POST['variable']) && $_POST['variable'] === $key) ? 'selected' : ''; ?>>
                                        <?php echo $variableNames[$key]; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <button type="submit" name="submit" class="btn btn-primary">Search & Save Data</button>
                </form>
            </div>
        </div>

        <!-- Search Results -->
        <?php if ($searchPerformed && !empty($climateData)): ?>
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h3 class="mb-0">Search Results: <?php echo $variableNames[$variable]; ?></h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Date</th>
                                    <th>Value</th>
                                    <th>Unit</th>
                                    <th>Source</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($climateData as $date => $data): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($date); ?></td>
                                        <td><?php echo htmlspecialchars($data['value']); ?></td>
                                        <td><?php echo htmlspecialchars($data['unit']); ?></td>
                                        <td><?php echo htmlspecialchars($data['source'] ?? 'N/A'); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Saved Data -->
        <?php if (!empty($savedData)): ?>
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h3 class="mb-0">Saved Climate Data</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Location</th>
                                    <th>Date</th>
                                    <th>Variable</th>
                                    <th>Value</th>
                                    <th>Saved At</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($savedData as $item): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($item['latitude'] . ', ' . $item['longitude']); ?></td>
                                        <td><?php echo htmlspecialchars($item['date']); ?></td>
                                        <td><?php echo htmlspecialchars($variableNames[$item['variable_code']] ?? $item['variable_code']); ?></td>
                                        <td><?php echo htmlspecialchars($item['value']) . ' ' . htmlspecialchars($unitMapping[$variableMapping[$item['variable_code']]] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars(date('Y-m-d H:i', strtotime($item['created_at']))); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Queensland stations data
        const qldMajorStations = [{
                id: '40842',
                name: 'Brisbane Airport',
                lat: -27.38,
                lon: 153.12
            },
            {
                id: '40214',
                name: 'Gold Coast Seaway',
                lat: -27.94,
                lon: 153.43
            },
            {
                id: '40866',
                name: 'Sunshine Coast Airport',
                lat: -26.60,
                lon: 153.09
            },
            {
                id: '33119',
                name: 'Mackay M.O',
                lat: -21.14,
                lon: 149.18
            },
            {
                id: '29063',
                name: 'Townsville Airport',
                lat: -19.25,
                lon: 146.77
            },
            {
                id: '31011',
                name: 'Cairns Airport',
                lat: -16.92,
                lon: 145.75
            },
            {
                id: '27045',
                name: 'Weipa Aero',
                lat: -12.67,
                lon: 141.92
            },
            {
                id: '38002',
                name: 'Birdsville Police',
                lat: -25.90,
                lon: 139.35
            },
            {
                id: '41004',
                name: 'Warwick (The Hermitage)',
                lat: -28.21,
                lon: 152.10
            },
            {
                id: '42023',
                name: 'Miles Post Office',
                lat: -26.66,
                lon: 150.19
            },
            {
                id: '36031',
                name: 'Longreach Aero',
                lat: -23.43,
                lon: 144.27
            },
            {
                id: '29127',
                name: 'Mount Isa Aero',
                lat: -20.67,
                lon: 139.48
            },
            {
                id: '39068',
                name: 'Mount Larcom Post Office',
                lat: -23.82,
                lon: 150.96
            },
            {
                id: '39061',
                name: 'Rockhampton Airport',
                lat: -23.38,
                lon: 150.47
            },
            {
                id: '37007',
                name: 'Charters Towers Airport',
                lat: -20.08,
                lon: 146.26
            },
            {
                id: '40035',
                name: 'Toowoomba Wellcamp Airport',
                lat: -27.56,
                lon: 151.79
            },
            {
                id: '40043',
                name: 'Kingaroy Airport',
                lat: -26.57,
                lon: 151.84
            },
            {
                id: '40126',
                name: 'Maryborough',
                lat: -25.54,
                lon: 152.70
            },
            {
                id: '33051',
                name: 'Mingela Post Office',
                lat: -19.95,
                lon: 146.94
            }
        ];

        const stationSelect = document.getElementById('qldStations');
        const stationDetails = document.querySelector('.station-details');
        const stationName = document.getElementById('selectedStationName');
        const stationCoords = document.getElementById('selectedStationCoords');
        const latInput = document.getElementById('latitude');
        const lonInput = document.getElementById('longitude');

        // Populate stations dropdown
        function populateStations() {
            qldMajorStations.sort((a, b) => a.name.localeCompare(b.name))
                .forEach(station => {
                    const option = document.createElement('option');
                    option.value = `${station.id}|${station.lat}|${station.lon}|${station.name}`;
                    option.textContent = `${station.name} (${station.lat}, ${station.lon})`;
                    stationSelect.appendChild(option);
                });
        }

        // Handle station selection
        stationSelect.addEventListener('change', function() {
            if (this.value) {
                const [id, lat, lon, name] = this.value.split('|');
                latInput.value = lat;
                lonInput.value = lon;
                stationName.textContent = name;
                stationCoords.textContent = `${lat}, ${lon}`;
                stationDetails.style.display = 'block';
            } else {
                stationDetails.style.display = 'none';
                stationName.textContent = 'N/A';
                stationCoords.textContent = 'N/A';
            }
        });

        // Clear station selection when coordinates are manually changed
        latInput.addEventListener('input', function() {
            stationSelect.value = '';
            stationDetails.style.display = 'none';
        });

        lonInput.addEventListener('input', function() {
            stationSelect.value = '';
            stationDetails.style.display = 'none';
        });

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            populateStations();

            // Check if current coordinates match any station
            const currentLat = parseFloat(latInput.value);
            const currentLon = parseFloat(lonInput.value);

            const matchedStation = qldMajorStations.find(station =>
                Math.abs(station.lat - currentLat) < 0.01 &&
                Math.abs(station.lon - currentLon) < 0.01
            );

            if (matchedStation) {
                stationSelect.value = `${matchedStation.id}|${matchedStation.lat}|${matchedStation.lon}|${matchedStation.name}`;
                stationName.textContent = matchedStation.name;
                stationCoords.textContent = `${matchedStation.lat}, ${matchedStation.lon}`;
                stationDetails.style.display = 'block';
            }
        });
    </script>
</body>

</html>