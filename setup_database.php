<?php
// Database setup script
require_once 'database.php';

try {
    // Create connection without database name first
    $host = 'localhost';
    $username = 'root';
    $password = '';
    
    $conn = new PDO("mysql:host=$host", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected successfully<br>";
    
    // Create database if not exists
    $conn->exec("CREATE DATABASE IF NOT EXISTS climate_data");
    echo "Database created or already exists<br>";
    
    // Switch to the database
    $conn->exec("USE climate_data");
    
    // Create locations table
    $sql = "CREATE TABLE IF NOT EXISTS locations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        latitude DECIMAL(10, 6) NOT NULL,
        longitude DECIMAL(10, 6) NOT NULL,
        elevation DECIMAL(10, 2),
        reference VARCHAR(10),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY lat_lon_unique (latitude, longitude)
    )";
    $conn->exec($sql);
    echo "Table 'locations' created or already exists<br>";
    
    // Create rainfall data table
    $sql = "CREATE TABLE IF NOT EXISTS rainfall_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        location_id INT NOT NULL,
        date DATE NOT NULL,
        value DECIMAL(10, 2) NOT NULL,
        source INT,
        variable_code VARCHAR(20) DEFAULT 'daily_rain',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY location_date_unique (location_id, date),
        FOREIGN KEY (location_id) REFERENCES locations(id)
    )";
    $conn->exec($sql);
    echo "Table 'rainfall_data' created or already exists<br>";
    
    echo "<br><strong>Database setup completed successfully!</strong>";
    
} catch(PDOException $e) {
    echo "Connection failed: " . $e->getMessage();
}
?>