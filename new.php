<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Climate Data Management System</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css" rel="stylesheet">
    <style>
        .sidebar {
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding-top: 20px;
            transition: all 0.3s;
        }
        
        .main-content {
            margin-left: 250px;
            padding: 20px;
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        
        .nav-link {
            color: white !important;
            padding: 15px 20px;
            border-radius: 8px;
            margin: 5px 15px;
            transition: all 0.3s;
        }
        
        .nav-link:hover, .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            transform: translateX(5px);
        }
        
        .card {
            border: none;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 15px;
            margin-bottom: 20px;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .data-table {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
        
        .hidden {
            display: none;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
        }
        
        .spinner-border {
            color: #667eea;
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="login-container">
        <div class="login-card">
            <div class="text-center mb-4">
                <i class="fas fa-cloud-sun fa-3x text-primary mb-3"></i>
                <h2 class="fw-bold">Climate Data System</h2>
                <p class="text-muted">Environmental Data Management Platform</p>
            </div>
            
            <form id="loginForm">
                <div class="mb-3">
                    <label for="username" class="form-label">Username</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                        <input type="text" class="form-control" id="username" required>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                        <input type="password" class="form-control" id="password" required>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-sign-in-alt me-2"></i>Login
                </button>
            </form>
            
            <div class="mt-4">
                <h6>Demo Accounts:</h6>
                <small class="text-muted">
                    Admin: admin/admin123<br>
                    Analyst: james/james123<br>
                    Researcher: harry/harry123<br>
                    Public: public/public123
                </small>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" class="hidden">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="text-center text-white mb-4">
                <i class="fas fa-cloud-sun fa-2x mb-2"></i>
                <h5>Climate Data</h5>
                <small id="userInfo"></small>
            </div>
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item" id="importDataNav">
                    <a class="nav-link" href="#" onclick="showSection('import')">
                        <i class="fas fa-download me-2"></i>Import Data
                    </a>
                </li>
                <li class="nav-item" id="dataViewNav">
                    <a class="nav-link" href="#" onclick="showSection('dataview')">
                        <i class="fas fa-table me-2"></i>View Data
                    </a>
                </li>
                <li class="nav-item" id="visualizationNav">
                    <a class="nav-link" href="#" onclick="showSection('visualization')">
                        <i class="fas fa-chart-line me-2"></i>Visualization
                    </a>
                </li>
                <li class="nav-item" id="userMgmtNav">
                    <a class="nav-link" href="#" onclick="showSection('users')">
                        <i class="fas fa-users me-2"></i>User Management
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="logout()">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Dashboard Section -->
            <div id="dashboardSection" class="content-section">
                <div class="row">
                    <div class="col-12">
                        <h2 class="mb-4">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </h2>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <i class="fas fa-database fa-2x mb-2"></i>
                            <h4 id="totalRecords">0</h4>
                            <p>Total Records</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <i class="fas fa-thermometer-half fa-2x mb-2"></i>
                            <h4 id="avgTemp">--°C</h4>
                            <p>Avg Temperature</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <i class="fas fa-cloud-rain fa-2x mb-2"></i>
                            <h4 id="totalRainfall">-- mm</h4>
                            <p>Total Rainfall</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <i class="fas fa-calendar fa-2x mb-2"></i>
                            <h4 id="lastUpdate">--</h4>
                            <p>Last Update</p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-area me-2"></i>Recent Data Trends</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="dashboardChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-list me-2"></i>Recent Activities</h5>
                            </div>
                            <div class="card-body">
                                <div id="recentActivities">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-download text-success me-2"></i>
                                        <span>Data imported for Brisbane location</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-eye text-info me-2"></i>
                                        <span>Temperature data viewed by Dr. Harry</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-chart-line text-warning me-2"></i>
                                        <span>Rainfall visualization generated</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Import Data Section -->
            <div id="importSection" class="content-section hidden">
                <div class="row">
                    <div class="col-12">
                        <h2 class="mb-4">
                            <i class="fas fa-download me-2"></i>Import Climate Data
                        </h2>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cloud me-2"></i>SILO API Data Import</h5>
                    </div>
                    <div class="card-body">
                        <form id="importForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="latitude" class="form-label">Latitude</label>
                                        <input type="number" class="form-control" id="latitude" 
                                               value="-27.4698" step="0.0001" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="longitude" class="form-label">Longitude</label>
                                        <input type="number" class="form-control" id="longitude" 
                                               value="153.0251" step="0.0001" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="startDate" class="form-label">Start Date</label>
                                        <input type="date" class="form-control" id="startDate" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="endDate" class="form-label">End Date</label>
                                        <input type="date" class="form-control" id="endDate" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="format" class="form-label">Data Format</label>
                                <select class="form-control" id="format">
                                    <option value="standard">Standard</option>
                                    <option value="alldata">All Data</option>
                                </select>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-download me-2"></i>Import Data
                            </button>
                        </form>
                        
                        <div id="importLoading" class="loading hidden">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Importing climate data...</p>
                        </div>
                        
                        <div id="importResult" class="mt-3"></div>
                    </div>
                </div>
            </div>

            <!-- Data View Section -->
            <div id="dataviewSection" class="content-section hidden">
                <div class="row">
                    <div class="col-12">
                        <h2 class="mb-4">
                            <i class="fas fa-table me-2"></i>Climate Data View
                        </h2>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-filter me-2"></i>Data Filters</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <input type="date" class="form-control" id="filterStartDate" placeholder="Start Date">
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control" id="filterEndDate" placeholder="End Date">
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="filterLocation">
                                    <option value="">All Locations</option>
                                    <option value="Brisbane">Brisbane</option>
                                    <option value="Sydney">Sydney</option>
                                    <option value="Melbourne">Melbourne</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-primary" onclick="filterData()">
                                    <i class="fas fa-search me-2"></i>Filter
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-database me-2"></i>Climate Data</h5>
                    </div>
                    <div class="card-body">
                        <div class="data-table">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Location</th>
                                        <th>Temperature (°C)</th>
                                        <th>Rainfall (mm)</th>
                                        <th>Humidity (%)</th>
                                        <th>Pressure (hPa)</th>
                                    </tr>
                                </thead>
                                <tbody id="dataTableBody">
                                    <!-- Data will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Visualization Section -->
            <div id="visualizationSection" class="content-section hidden">
                <div class="row">
                    <div class="col-12">
                        <h2 class="mb-4">
                            <i class="fas fa-chart-line me-2"></i>Data Visualization
                        </h2>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cog me-2"></i>Visualization Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <select class="form-control" id="chartType">
                                    <option value="line">Line Chart</option>
                                    <option value="bar">Bar Chart</option>
                                    <option value="scatter">Scatter Plot</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <select class="form-control" id="dataType">
                                    <option value="temperature">Temperature</option>
                                    <option value="rainfall">Rainfall</option>
                                    <option value="humidity">Humidity</option>
                                    <option value="pressure">Pressure</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-primary" onclick="generateVisualization()">
                                    <i class="fas fa-chart-bar me-2"></i>Generate
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-area me-2"></i>Climate Data Visualization</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="visualizationChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Management Section -->
            <div id="usersSection" class="content-section hidden">
                <div class="row">
                    <div class="col-12">
                        <h2 class="mb-4">
                            <i class="fas fa-users me-2"></i>User Management
                        </h2>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user-plus me-2"></i>Add New User</h5>
                    </div>
                    <div class="card-body">
                        <form id="userForm">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="newUsername" class="form-label">Username</label>
                                        <input type="text" class="form-control" id="newUsername" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="newPassword" class="form-label">Password</label>
                                        <input type="password" class="form-control" id="newPassword" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="userRole" class="form-label">Role</label>
                                        <select class="form-control" id="userRole" required>
                                            <option value="analyst">Data Analyst</option>
                                            <option value="researcher">Researcher</option>
                                            <option value="officer">Environmental Officer</option>
                                            <option value="public">Public User</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add User
                            </button>
                        </form>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list me-2"></i>Existing Users</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Username</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                <!-- Users will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    
    <script>
        // Application State
        let currentUser = null;
        let climateData = [];
        let dashboardChart = null;
        let visualizationChart = null;
        
        // Sample users database
        const users = [
            { username: 'admin', password: 'admin123', role: 'admin', name: 'System Administrator' },
            { username: 'james', password: 'james123', role: 'analyst', name: 'James - Climate Data Analyst' },
            { username: 'harry', password: 'harry123', role: 'researcher', name: 'Dr. Harry - Climate Researcher' },
            { username: 'public', password: 'public123', role: 'public', name: 'Public User' }
        ];
        
        // Sample climate data
        const sampleData = [
            { date: '2024-01-01', location: 'Brisbane', temperature: 28.5, rainfall: 0.2, humidity: 65, pressure: 1013.2 },
            { date: '2024-01-02', location: 'Brisbane', temperature: 30.1, rainfall: 0.0, humidity: 68, pressure: 1012.8 },
            { date: '2024-01-03', location: 'Brisbane', temperature: 29.8, rainfall: 1.5, humidity: 72, pressure: 1011.5 },
            { date: '2024-01-04', location: 'Brisbane', temperature: 27.3, rainfall: 5.2, humidity: 78, pressure: 1010.2 },
            { date: '2024-01-05', location: 'Brisbane', temperature: 26.1, rainfall: 12.3, humidity: 82, pressure: 1009.8 }
        ];
        
        // Initialize application
        document.addEventListener('DOMContentLoaded', function() {
            climateData = [...sampleData];
            setDefaultDates();
            loadUsers();
        });
        
        // Set default dates
        function setDefaultDates() {
            const today = new Date();
            const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
            
            document.getElementById('startDate').value = lastMonth.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
        }
        
        // Login functionality
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            const user = users.find(u => u.username === username && u.password === password);
            
            if (user) {
                currentUser = user;
                showMainApp();
            } else {
                alert('Invalid credentials. Please try again.');
            }
        });
        
        // Show main application
        function showMainApp() {
            document.getElementById('loginScreen').classList.add('hidden');
            document.getElementById('mainApp').classList.remove('hidden');
            
            document.getElementById('userInfo').textContent = currentUser.name;
            
            // Set navigation visibility based on user role
            setNavigationPermissions();
            
            // Load dashboard
            showSection('dashboard');
            loadDashboard();
        }
        
        // Set navigation permissions
        function setNavigationPermissions() {
            const importNav = document.getElementById('importDataNav');
            const userMgmtNav = document.getElementById('userMgmtNav');
            const visualizationNav = document.getElementById('visualizationNav');
            
            // Hide import for public users
            if (currentUser.role === 'public') {
                importNav.classList.add('hidden');
            }
            
            // Show user management only for admin
            if (currentUser.role !== 'admin') {
                userMgmtNav.classList.add('hidden');
            }
            
            // Show visualization for researchers
            if (currentUser.role === 'researcher' || currentUser.role === 'admin') {
                visualizationNav.classList.remove('hidden');
            }
        }
        
        // Show section
        function showSection(section) {
            // Hide all sections
            document.querySelectorAll('.content-section').forEach(s => s.classList.add('hidden'));
            
            // Remove active class from nav links
            document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
            
            // Show selected section
            document.getElementById(section + 'Section').classList.remove('hidden');
            
            // Add active class to clicked nav link
            event.target.classList.add('active');
            
            // Load section specific data
            if (section === 'dataview') {
                loadDataView();
            } else if (section === 'users') {
                loadUsers();
            }
        }
        
        // Load dashboard
        function loadDashboard() {
            // Update statistics
            document.getElementById('totalRecords').textContent = climateData.length;
            
            if (climateData.length > 0) {
                const avgTemp = climateData.reduce((sum, d) => sum + d.temperature, 0) / climateData.length;
                const totalRain = climateData.reduce((sum, d) => sum + d.rainfall, 0);
                
                document.getElementById('avgTemp').textContent = avgTemp.toFixed(1) + '°C';
                document.getElementById('totalRainfall').textContent = totalRain.toFixed(1) + ' mm';
                document.getElementById('lastUpdate').textContent = new Date().toLocaleDateString();
            }
            
            // Create dashboard chart
            createDashboardChart();
        }
        
        // Create dashboard chart
        function createDashboardChart() {
            const ctx = document.getElementById('dashboardChart').getContext('2d');
            
            if (dashboardChart) {
                dashboardChart.destroy();
            }
            
            const labels = climateData.map(d => d.date);
            const temperatures = climateData.map(d => d.temperature);
            
            dashboardChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Temperature (°C)',
                        data: temperatures,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false
                        }
                    }
                }
            });
        }
        
        // Import data form
        document.getElementById('importForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const latitude = document.getElementById('latitude').value;
            const longitude = document.getElementById('longitude').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const format = document.getElementById('format').value;
            
            // Show loading
            document.getElementById('importLoading').classList.remove('hidden');
            document.getElementById('importResult').innerHTML = '';
            
            // Simulate API call
            setTimeout(() => {
                // Generate sample data for the date range
                const start = new Date(startDate);
                const end = new Date(endDate);
                const newData = [];
                
                for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
                    newData.push({
                        date: d.toISOString().split('T')[0],
                        location: 'Brisbane',
                        temperature: 20 + Math.random() * 15,
                        rainfall: Math.random() * 10,
                        humidity: 50 + Math.random() * 30,
                        pressure: 1000 + Math.random() * 20
                    });
                }
                
                // Add to climate data
                climateData = [...climateData, ...newData];
                
                // Hide loading
                document.getElementById('importLoading').classList.add('hidden');
                
                // Show result
                document.getElementById('importResult').innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        Successfully imported ${newData.length} records from SILO API.
                    </div>
                `;
                
                // Update dashboard if visible
                if (!document.getElementById('dashboardSection').classList.contains('hidden')) {
                    loadDashboard();
                }
            }, 2000);
        });
        
        // Load data view
        function loadDataView() {
            const tbody = document.getElementById('dataTableBody');
            tbody.innerHTML = '';
            
            climateData.forEach(row => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${row.date}</td>
                    <td>${row.location}</td>
                    <td>${row.temperature.toFixed(1)}</td>
                    <td>${row.rainfall.toFixed(1)}</td>
                    <td>${row.humidity.toFixed(0)}</td>
                    <td>${row.pressure.toFixed(1)}</td>
                `;
                tbody.appendChild(tr);
            });
        }
        
        // Filter data
        function filterData() {
            const startDate = document.getElementById('filterStartDate').value;
            const endDate = document.getElementById('filterEndDate').value;
            const location = document.getElementById('filterLocation').value;
            
            let filteredData = climateData;
            
            if (startDate) {
                filteredData = filteredData.filter(d => d.date >= startDate);
            }
            
            if (endDate) {
                filteredData = filteredData.filter(d => d.date <= endDate);
            }
            
            if (location) {
                filteredData = filteredData.filter(d => d.location === location);
            }
            
            // Update table
            const tbody = document.getElementById('dataTableBody');
            tbody.innerHTML = '';
            
            filteredData.forEach(row => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${row.date}</td>
                    <td>${row.location}</td>
                    <td>${row.temperature.toFixed(1)}</td>
                    <td>${row.rainfall.toFixed(1)}</td>
                    <td>${row.humidity.toFixed(0)}</td>
                    <td>${row.pressure.toFixed(1)}</td>
                `;
                tbody.appendChild(tr);
            });
        }
        
        // Generate visualization
        function generateVisualization() {
            const chartType = document.getElementById('chartType').value;
            const dataType = document.getElementById('dataType').value;
            
            const ctx = document.getElementById('visualizationChart').getContext('2d');
            
            if (visualizationChart) {
                visualizationChart.destroy();
            }
            
            const labels = climateData.map(d => d.date);
            let data, label, color;
            
            switch (dataType) {
                case 'temperature':
                    data = climateData.map(d => d.temperature);
                    label = 'Temperature (°C)';
                    color = '#ff6b6b';
                    break;
                case 'rainfall':
                    data = climateData.map(d => d.rainfall);
                    label = 'Rainfall (mm)';
                    color = '#4ecdc4';
                    break;
                case 'humidity':
                    data = climateData.map(d => d.humidity);
                    label = 'Humidity (%)';
                    color = '#45b7d1';
                    break;
                case 'pressure':
                    data = climateData.map(d => d.pressure);
                    label = 'Pressure (hPa)';
                    color = '#96ceb4';
                    break;
            }
            
            visualizationChart = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: labels,
                    datasets: [{
                        label: label,
                        data: data,
                        borderColor: color,
                        backgroundColor: chartType === 'line' ? 
                            color.replace('rgb', 'rgba').replace(')', ', 0.1)') : color,
                        tension: chartType === 'line' ? 0.4 : 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: dataType === 'rainfall'
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: `${label} Over Time`
                        }
                    }
                }
            });
        }
        
        // User management
        document.getElementById('userForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('newUsername').value;
            const password = document.getElementById('newPassword').value;
            const role = document.getElementById('userRole').value;
            
            // Check if user already exists
            if (users.find(u => u.username === username)) {
                alert('Username already exists!');
                return;
            }
            
            // Add new user
            const newUser = {
                username: username,
                password: password,
                role: role,
                name: `${username} - ${role.charAt(0).toUpperCase() + role.slice(1)}`
            };
            
            users.push(newUser);
            
            // Clear form
            document.getElementById('userForm').reset();
            
            // Reload users table
            loadUsers();
            
            alert('User added successfully!');
        });
        
        // Load users table
        function loadUsers() {
            const tbody = document.getElementById('usersTableBody');
            tbody.innerHTML = '';
            
            users.forEach((user, index) => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${user.username}</td>
                    <td>
                        <span class="badge bg-${getRoleBadgeColor(user.role)}">${user.role}</span>
                    </td>
                    <td>
                        <span class="badge bg-success">Active</span>
                    </td>
                    <td>
                        ${user.username !== 'admin' ? `
                            <button class="btn btn-sm btn-outline-danger" 
                                    onclick="deleteUser(${index})">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : '<span class="text-muted">Protected</span>'}
                    </td>
                `;
                tbody.appendChild(tr);
            });
        }
        
        // Get role badge color
        function getRoleBadgeColor(role) {
            switch (role) {
                case 'admin': return 'danger';
                case 'analyst': return 'primary';
                case 'researcher': return 'info';
                case 'officer': return 'warning';
                case 'public': return 'secondary';
                default: return 'secondary';
            }
        }
        
        // Delete user
        function deleteUser(index) {
            if (confirm('Are you sure you want to delete this user?')) {
                users.splice(index, 1);
                loadUsers();
            }
        }
        
        // Logout
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                currentUser = null;
                document.getElementById('loginScreen').classList.remove('hidden');
                document.getElementById('mainApp').classList.add('hidden');
                
                // Clear login form
                document.getElementById('loginForm').reset();
                
                // Clear charts
                if (dashboardChart) {
                    dashboardChart.destroy();
                    dashboardChart = null;
                }
                if (visualizationChart) {
                    visualizationChart.destroy();
                    visualizationChart = null;
                }
            }
        }
    </script>
</body>
</html>

<?php
// PHP Backend Code (save as separate files)

/*
=== database.php ===
*/
class Database {
    private $host = 'localhost';
    private $dbname = 'climate_data';
    private $username = 'root';
    private $password = '';
    private $pdo;
    
    public function __construct() {
        try {
            $this->pdo = new PDO(
                "mysql:host={$this->host};dbname={$this->dbname}",
                $this->username,
                $this->password,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
        } catch (PDOException $e) {
            die("Database connection failed: " . $e->getMessage());
        }
    }
    
    public function getConnection() {
        return $this->pdo;
    }
}

/*
=== setup_database.sql ===
CREATE DATABASE IF NOT EXISTS climate_data;
USE climate_data;

CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'analyst', 'researcher', 'officer', 'public') NOT NULL,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS climate_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL,
    location VARCHAR(100) NOT NULL,
    latitude DECIMAL(10, 6) NOT NULL,
    longitude DECIMAL(10, 6) NOT NULL,
    temperature DECIMAL(5, 2),
    rainfall DECIMAL(8, 2),
    humidity DECIMAL(5, 2),
    pressure DECIMAL(7, 2),
    wind_speed DECIMAL(5, 2),
    wind_direction INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_date (date),
    INDEX idx_location (location)
);

CREATE TABLE IF NOT EXISTS data_imports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    source VARCHAR(50) NOT NULL,
    records_count INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    latitude DECIMAL(10, 6) NOT NULL,
    longitude DECIMAL(10, 6) NOT NULL,
    status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert default users
INSERT INTO users (username, password, role, name) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'System Administrator'),
('james', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'analyst', 'James - Climate Data Analyst'),
('harry', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'researcher', 'Dr. Harry - Climate Researcher'),
('public', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'public', 'Public User');
*/

/*
=== auth.php ===
*/
session_start();
require_once 'database.php';

class Auth {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    public function login($username, $password) {
        $stmt = $this->db->getConnection()->prepare(
            "SELECT * FROM users WHERE username = ?"
        );
        $stmt->execute([$username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user && password_verify($password, $user['password'])) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['name'] = $user['name'];
            return true;
        }
        
        return false;
    }
    
    public function logout() {
        session_destroy();
    }
    
    public function isLoggedIn() {
        return isset($_SESSION['user_id']);
    }
    
    public function getCurrentUser() {
        if ($this->isLoggedIn()) {
            return [
                'id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'],
                'role' => $_SESSION['role'],
                'name' => $_SESSION['name']
            ];
        }
        return null;
    }
    
    public function hasPermission($permission) {
        if (!$this->isLoggedIn()) return false;
        
        $role = $_SESSION['role'];
        
        switch ($permission) {
            case 'import_data':
                return in_array($role, ['admin', 'analyst', 'officer']);
            case 'manage_users':
                return $role === 'admin';
            case 'view_visualizations':
                return in_array($role, ['admin', 'researcher', 'analyst']);
            case 'view_data':
                return true; // All users can view data
            default:
                return false;
        }
    }
}

/*
=== silo_api.php ===
*/
class SiloAPI {
    private $baseUrl = 'https://www.longpaddock.qld.gov.au/cgi-bin/silo';
    
    public function fetchData($latitude, $longitude, $startDate, $endDate, $format = 'standard') {
        $url = $this->baseUrl . '/PatchedPointDataset.php?' . http_build_query([
            'lat' => $latitude,
            'lon' => $longitude,
            'start' => $startDate,
            'finish' => $endDate,
            'format' => $format,
            'username' => 'demo', // Replace with actual credentials
            'password' => 'demo'
        ]);
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 30,
                'user_agent' => 'Climate Data Management System'
            ]
        ]);
        
        $response = file_get_contents($url, false, $context);
        
        if ($response === false) {
            throw new Exception('Failed to fetch data from SILO API');
        }
        
        return $this->parseResponse($response);
    }
    
    private function parseResponse($response) {
        $lines = explode("\n", trim($response));
        $data = [];
        
        // Skip header lines (usually start with *)
        $dataStarted = false;
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            if (empty($line) || strpos($line, '*') === 0) {
                continue;
            }
            
            if (!$dataStarted && preg_match('/^\d{4}/', $line)) {
                $dataStarted = true;
            }
            
            if ($dataStarted) {
                $fields = preg_split('/\s+/', $line);
                
                if (count($fields) >= 8) {
                    $data[] = [
                        'date' => $this->formatDate($fields[0]),
                        'day_of_year' => intval($fields[1]),
                        'temperature_max' => floatval($fields[2]),
                        'temperature_min' => floatval($fields[3]),
                        'rainfall' => floatval($fields[4]),
                        'evaporation' => floatval($fields[5]),
                        'radiation' => floatval($fields[6]),
                        'vapour_pressure' => floatval($fields[7])
                    ];
                }
            }
        }
        
        return $data;
    }
    
    private function formatDate($dateString) {
        // Convert YYYYMMDD to YYYY-MM-DD
        if (strlen($dateString) === 8) {
            return substr($dateString, 0, 4) . '-' . 
                   substr($dateString, 4, 2) . '-' . 
                   substr($dateString, 6, 2);
        }
        return $dateString;
    }
}

/*
=== api_endpoints.php ===
*/
header('Content-Type: application/json');
require_once 'auth.php';
require_once 'silo_api.php';

$auth = new Auth();
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Handle different API endpoints
switch ($path) {
    case '/api/login':
        handleLogin();
        break;
    case '/api/logout':
        handleLogout();
        break;
    case '/api/import-data':
        handleImportData();
        break;
    case '/api/climate-data':
        handleClimateData();
        break;
    case '/api/users':
        handleUsers();
        break;
    case '/api/dashboard':
        handleDashboard();
        break;
    default:
        http_response_code(404);
        echo json_encode(['error' => 'Endpoint not found']);
}

function handleLogin() {
    global $auth;
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $username = $input['username'] ?? '';
    $password = $input['password'] ?? '';
    
    if ($auth->login($username, $password)) {
        echo json_encode([
            'success' => true,
            'user' => $auth->getCurrentUser()
        ]);
    } else {
        http_response_code(401);
        echo json_encode(['error' => 'Invalid credentials']);
    }
}

function handleLogout() {
    global $auth;
    $auth->logout();
    echo json_encode(['success' => true]);
}

function handleImportData() {
    global $auth;
    
    if (!$auth->isLoggedIn() || !$auth->hasPermission('import_data')) {
        http_response_code(403);
        echo json_encode(['error' => 'Permission denied']);
        return;
    }
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    try {
        $siloAPI = new SiloAPI();
        $data = $siloAPI->fetchData(
            $input['latitude'],
            $input['longitude'],
            $input['start_date'],
            $input['end_date'],
            $input['format'] ?? 'standard'
        );
        
        // Store data in database
        $db = new Database();
        $pdo = $db->getConnection();
        
        $stmt = $pdo->prepare(
            "INSERT INTO climate_data (date, location, latitude, longitude, temperature, rainfall, humidity, pressure) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
        );
        
        $location = "Lat: {$input['latitude']}, Lon: {$input['longitude']}";
        $imported = 0;
        
        foreach ($data as $record) {
            $temperature = ($record['temperature_max'] + $record['temperature_min']) / 2;
            $stmt->execute([
                $record['date'],
                $location,
                $input['latitude'],
                $input['longitude'],
                $temperature,
                $record['rainfall'],
                rand(50, 90), // Simulated humidity
                rand(1000, 1020) // Simulated pressure
            ]);
            $imported++;
        }
        
        // Log import
        $stmt = $pdo->prepare(
            "INSERT INTO data_imports (user_id, source, records_count, start_date, end_date, latitude, longitude, status) 
             VALUES (?, 'SILO', ?, ?, ?, ?, ?, 'completed')"
        );
        $stmt->execute([
            $auth->getCurrentUser()['id'],
            $imported,
            $input['start_date'],
            $input['end_date'],
            $input['latitude'],
            $input['longitude']
        ]);
        
        echo json_encode([
            'success' => true,
            'records_imported' => $imported
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function handleClimateData() {
    global $auth;
    
    if (!$auth->isLoggedIn()) {
        http_response_code(403);
        echo json_encode(['error' => 'Authentication required']);
        return;
    }
    
    $db = new Database();
    $pdo = $db->getConnection();
    
    $where = [];
    $params = [];
    
    // Apply filters
    if (isset($_GET['start_date'])) {
        $where[] = "date >= ?";
        $params[] = $_GET['start_date'];
    }
    
    if (isset($_GET['end_date'])) {
        $where[] = "date <= ?";
        $params[] = $_GET['end_date'];
    }
    
    if (isset($_GET['location'])) {
        $where[] = "location LIKE ?";
        $params[] = "%{$_GET['location']}%";
    }
    
    $whereClause = empty($where) ? '' : 'WHERE ' . implode(' AND ', $where);
    
    $stmt = $pdo->prepare(
        "SELECT * FROM climate_data {$whereClause} ORDER BY date DESC LIMIT 1000"
    );
    $stmt->execute($params);
    
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode($data);
}

function handleUsers() {
    global $auth;
    
    if (!$auth->isLoggedIn() || !$auth->hasPermission('manage_users')) {
        http_response_code(403);
        echo json_encode(['error' => 'Permission denied']);
        return;
    }
    
    $db = new Database();
    $pdo = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $stmt = $pdo->prepare("SELECT id, username, role, name, created_at FROM users");
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo json_encode($users);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $stmt = $pdo->prepare(
            "INSERT INTO users (username, password, role, name) VALUES (?, ?, ?, ?)"
        );
        
        $hashedPassword = password_hash($input['password'], PASSWORD_DEFAULT);
        
        try {
            $stmt->execute([
                $input['username'],
                $hashedPassword,
                $input['role'],
                $input['name'] ?? $input['username']
            ]);
            
            echo json_encode(['success' => true]);
        } catch (PDOException $e) {
            http_response_code(400);
            echo json_encode(['error' => 'Username already exists']);
        }
    }
}

function handleDashboard() {
    global $auth;
    
    if (!$auth->isLoggedIn()) {
        http_response_code(403);
        echo json_encode(['error' => 'Authentication required']);
        return;
    }
    
    $db = new Database();
    $pdo = $db->getConnection();
    
    // Get statistics
    $stats = [];
    
    // Total records
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM climate_data");
    $stmt->execute();
    $stats['total_records'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Average temperature
    $stmt = $pdo->prepare("SELECT AVG(temperature) as avg_temp FROM climate_data WHERE temperature IS NOT NULL");
    $stmt->execute();
    $stats['avg_temperature'] = round($stmt->fetch(PDO::FETCH_ASSOC)['avg_temp'], 1);
    
    // Total rainfall
    $stmt = $pdo->prepare("SELECT SUM(rainfall) as total_rain FROM climate_data WHERE rainfall IS NOT NULL");
    $stmt->execute();
    $stats['total_rainfall'] = round($stmt->fetch(PDO::FETCH_ASSOC)['total_rain'], 1);
    
    // Recent data for chart
    $stmt = $pdo->prepare(
        "SELECT date, temperature, rainfall FROM climate_data 
         WHERE temperature IS NOT NULL 
         ORDER BY date DESC LIMIT 30"
    );
    $stmt->execute();
    $stats['recent_data'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode($stats);
}
?>