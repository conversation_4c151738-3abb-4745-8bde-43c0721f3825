<?php
// Enhanced SILO Climate Data Application - FIXED VARIABLE MAPPINGS
session_start();

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Generate CSRF token
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Configuration
$apiBaseUrl = "https://www.longpaddock.qld.gov.au/cgi-bin/silo/";
$apiUsername = "<EMAIL>";
$apiPassword = "apirequest";

// FIXED: Consistent variable mapping with SILO comment codes
$variableMapping = [
    'R' => 'Daily Rainfall',
    'X' => 'Maximum Temperature', 
    'N' => 'Minimum Temperature',
    'J' => 'Solar Radiation',
    'V' => 'Vapour Pressure',
    'E' => 'Evaporation (Class A pan)',
    'H' => 'Relative Humidity (max temp)',
    'G' => 'Relative Humidity (min temp)'
];

// FIXED: Unit mapping using the same keys as variableMapping
$unitMapping = [
    'R' => 'mm',           // Daily Rainfall
    'X' => '°C',           // Maximum Temperature
    'N' => '°C',           // Minimum Temperature
    'J' => 'MJ/m²',        // Solar Radiation
    'V' => 'hPa',          // Vapour Pressure
    'E' => 'mm',           // Evaporation
    'H' => '%',            // Relative Humidity (max temp)
    'G' => '%'             // Relative Humidity (min temp)
];

// FIXED: Variable display names using the same keys
$variableNames = [
    'R' => 'Daily Rainfall',
    'X' => 'Maximum Temperature',
    'N' => 'Minimum Temperature', 
    'J' => 'Daily Solar Radiation',
    'V' => 'Vapour Pressure',
    'E' => 'Evaporation (Class A pan)',
    'H' => 'Relative Humidity (max temp)',
    'G' => 'Relative Humidity (min temp)'
];

/**
 * Database connection
 */
function getDbConnection()
{
    $host = 'localhost';
    $dbname = 'climate_data';
    $username = 'root';
    $password = '';

    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        error_log("Database connection error: " . $e->getMessage());
        return false;
    }
}

/**
 * Create database tables if they don't exist
 */
function createTables()
{
    $conn = getDbConnection();
    if (!$conn) {
        return false;
    }

    try {
        // Create locations table
        $conn->exec("CREATE TABLE IF NOT EXISTS locations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            latitude DECIMAL(10, 6) NOT NULL,
            longitude DECIMAL(10, 6) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_coords (latitude, longitude)
        )");

        // Create climate_data table
        $conn->exec("CREATE TABLE IF NOT EXISTS climate_data (
            id INT AUTO_INCREMENT PRIMARY KEY,
            location_id INT NOT NULL,
            date DATE NOT NULL,
            variable_code VARCHAR(20) NOT NULL,
            variable_name VARCHAR(100) NOT NULL,
            value DECIMAL(10, 4) NOT NULL,
            unit VARCHAR(10) NOT NULL,
            source VARCHAR(10) DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE CASCADE,
            UNIQUE KEY unique_record (location_id, date, variable_code)
        )");

        return true;
    } catch (PDOException $e) {
        error_log("Table creation error: " . $e->getMessage());
        return false;
    }
}

/**
 * Fetch data from SILO API - FIXED VERSION
 */
function fetchFromAPI($endpoint, $params)
{
    global $apiBaseUrl;
    $url = $apiBaseUrl . $endpoint . "?" . http_build_query($params);

    // Debug: Log the API URL
    error_log("API URL: " . $url);

    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false, // SILO API may have SSL issues
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_USERAGENT => 'Climate Data App/1.0',
        CURLOPT_HTTPHEADER => [
            'Accept: application/json, text/plain, */*',
            'Content-Type: application/x-www-form-urlencoded'
        ]
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);

    curl_close($ch);

    if ($curlError) {
        error_log("CURL Error: " . $curlError);
        return ['error' => 'API Connection Error: ' . $curlError];
    }

    if ($httpCode !== 200) {
        error_log("HTTP Error: " . $httpCode . " Response: " . $response);
        return ['error' => "API returned HTTP $httpCode. Response: " . substr($response, 0, 200)];
    }

    // Debug: Log the raw response
    error_log("Raw API Response: " . substr($response, 0, 500));

    // Try JSON parsing first
    $data = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        return $data;
    }

    // SILO API often returns CSV or plain text, not JSON
    // Let's try to parse it as CSV
    if (strpos($response, 'Date') !== false || strpos($response, 'date') !== false) {
        // This looks like CSV data
        return parseCSVResponse($response);
    }

    // If neither works, return the raw response for debugging
    return ['error' => 'Unable to parse API response', 'raw_response' => $response];
}

/**
 * Parse CSV response from SILO API - UPDATED FOR JSON HANDLING
 */
function parseCSVResponse($csvData)
{
    $lines = explode("\n", trim($csvData));
    if (empty($lines)) {
        return ['error' => 'Empty CSV response'];
    }

    // Find header line
    $headerLine = -1;
    for ($i = 0; $i < count($lines); $i++) {
        if (stripos($lines[$i], 'Date') !== false || stripos($lines[$i], 'date') !== false) {
            $headerLine = $i;
            break;
        }
    }

    if ($headerLine === -1) {
        return ['error' => 'No header found in CSV response'];
    }

    $headers = str_getcsv($lines[$headerLine]);
    $data = [];

    // Process data lines
    for ($i = $headerLine + 1; $i < count($lines); $i++) {
        if (trim($lines[$i]) === '') continue;

        $row = str_getcsv($lines[$i]);
        if (count($row) >= count($headers)) {
            $rowData = array_combine($headers, $row);
            $data[] = $rowData;
        }
    }

    return ['data' => $data, 'format' => 'csv'];
}

/**
 * Save climate data to database
 */
function saveClimateData($latitude, $longitude, $variable, $climateData, $variableNames, $unitMapping)
{
    $conn = getDbConnection();
    if (!$conn) {
        return "Database connection failed";
    }

    try {
        $conn->beginTransaction();

        // Insert location
        $stmtLoc = $conn->prepare("INSERT IGNORE INTO locations (latitude, longitude) VALUES (:lat, :lon)");
        $stmtLoc->execute([':lat' => $latitude, ':lon' => $longitude]);

        // Get location ID
        $stmt = $conn->prepare("SELECT id FROM locations WHERE latitude = :lat AND longitude = :lon");
        $stmt->execute([':lat' => $latitude, ':lon' => $longitude]);
        $locationId = $stmt->fetchColumn();

        if (!$locationId) {
            throw new Exception("Failed to get location ID");
        }

        // Insert climate data
        $stmtClimate = $conn->prepare("INSERT INTO climate_data 
                                     (location_id, date, variable_code, variable_name, value, unit, source) 
                                     VALUES (:location_id, :date, :variable_code, :variable_name, :value, :unit, :source)
                                     ON DUPLICATE KEY UPDATE 
                                     value = :value, variable_name = :variable_name, unit = :unit, source = :source");

        foreach ($climateData as $date => $data) {
            $stmtClimate->execute([
                ':location_id' => $locationId,
                ':date' => $date,
                ':variable_code' => $variable,
                ':variable_name' => $variableNames[$variable],
                ':value' => $data['value'],
                ':unit' => $data['unit'],
                ':source' => $data['source']
            ]);
        }

        $conn->commit();
        return true;
    } catch (Exception $e) {
        $conn->rollBack();
        return "Database error: " . $e->getMessage();
    }
}

/**
 * Get saved climate data from database
 */
function getSavedClimateData()
{
    $conn = getDbConnection();
    if (!$conn) {
        return [];
    }

    try {
        $stmt = $conn->query("SELECT l.latitude, l.longitude, 
                             c.date, c.variable_code, c.variable_name, c.value, c.unit, c.source, c.created_at
                             FROM locations l
                             JOIN climate_data c ON l.id = c.location_id
                             ORDER BY c.created_at DESC, c.date ASC
                             LIMIT 100");

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        return [];
    }
}

// Initialize database tables
createTables();

// Initialize variables
$climateData = [];
$error = '';
$message = '';
$searchPerformed = false;
$apiUrl = '';
$debugInfo = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit'])) {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $error = 'Security validation failed. Please try again.';
    } else {
        // Get and validate inputs
        $latitude = filter_input(INPUT_POST, 'latitude', FILTER_VALIDATE_FLOAT);
        $longitude = filter_input(INPUT_POST, 'longitude', FILTER_VALIDATE_FLOAT);
        @$startDate = filter_input(INPUT_POST, 'start_date', FILTER_SANITIZE_STRING);
        @$endDate = filter_input(INPUT_POST, 'end_date', FILTER_SANITIZE_STRING);
        @$variable = filter_input(INPUT_POST, 'variable', FILTER_SANITIZE_STRING);

        // Validate inputs
        if ($latitude === false || $longitude === false) {
            $error = 'Invalid coordinates';
        } elseif (empty($startDate) || empty($endDate)) {
            $error = 'Date range required';
        } elseif (empty($variable) || !isset($variableMapping[$variable])) {
            $error = 'Invalid climate variable';
        } elseif ($latitude < -29.0 || $latitude > -10.0 || $longitude < 138.0 || $longitude > 155.0) {
            $error = 'Coordinates must be within Queensland boundaries';
        } else {
            // FIXED: Prepare API parameters with correct format
            $params = [
                'lat' => round($latitude * 20) / 20, // Round to nearest 0.05 degrees
                'lon' => round($longitude * 20) / 20,
                'start' => str_replace('-', '', $startDate), // Format: YYYYMMDD
                'finish' => str_replace('-', '', $endDate),
                'format' => 'json',
                'comment' => $variable, // Use the actual variable code
                'username' => $apiUsername,
                'password' => $apiPassword
            ];

            // Store the API URL for display
            $apiUrl = $apiBaseUrl . 'DataDrillDataset.php?' . http_build_query($params);
            $debugInfo = "API URL: " . $apiUrl . "\n";
            $debugInfo .= "Parameters: " . print_r($params, true);

            // Fetch data from API
            $response = fetchFromAPI('DataDrillDataset.php', $params);

            if (isset($response['error'])) {
                $error = $response['error'];
                if (isset($response['raw_response'])) {
                    $debugInfo .= "\nRaw Response: " . $response['raw_response'];
                }
            } else {
                // Process the JSON response
    if (isset($response['data']) && is_array($response['data'])) {
        // This is the new SILO API format with nested structure
        foreach ($response['data'] as $item) {
            if (isset($item['date']) && isset($item['variables']) && is_array($item['variables'])) {
                $dateValue = $item['date'];
                
                // Process each variable in the variables array
                foreach ($item['variables'] as $varData) {
                    // Check if this variable matches what we requested
                    if (isset($varData['variable_code']) && isset($varData['value'])) {
                        $varCode = $varData['variable_code'];
                        $dataValue = $varData['value'];
                        $source = isset($varData['source']) ? $varData['source'] : 'SILO';
                        
                        // Map variable codes to our expected format
                        $variableCodeMap = [
                            'max_temp' => 'X',
                            'min_temp' => 'N', 
                            'rainfall' => 'R',
                            'solar_radiation' => 'J',
                            'vapour_pressure' => 'V',
                            'evaporation' => 'E',
                            'rh_tmax' => 'H',
                            'rh_tmin' => 'G'
                        ];
                        
                        // Check if this variable matches our request
                        $mappedVar = isset($variableCodeMap[$varCode]) ? $variableCodeMap[$varCode] : $varCode;
                        
                        // Only process if it matches the requested variable or if we want all variables
                        if ($mappedVar === $variable || $varCode === $variable) {
                            $climateData[$dateValue] = [
                                'value' => round(floatval($dataValue), 2),
                                'unit' => $unitMapping[$variable] ?? 'N/A',
                                'source' => 'SILO-' . $source,
                                'variable_code' => $varCode
                            ];
                        }
                    }
                }
            }
        }

            }
        }
    }
}

// Get saved data for display
$savedData = getSavedClimateData();
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Queensland Climate Data Explorer - Fixed</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px 0;
        }

        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }

        .table-responsive {
            max-height: 400px;
            overflow-y: auto;
        }

        .api-url-display {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            border: 1px solid #dee2e6;
            word-break: break-all;
        }

        .api-url-display code {
            font-size: 11px;
            color: #0066cc;
            line-height: 1.4;
        }

        .variable-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 15px;
        }

        .debug-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 15px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1 class="text-center mb-4">Queensland Climate Data Explorer - Fixed</h1>

        <!-- Variable Information -->
        <div class="variable-info">
            <h5>Available Climate Variables:</h5>
            <div class="row">
                <?php foreach ($variableNames as $code => $name): ?>
                    <div class="col-md-4 mb-2">
                        <strong><?php echo htmlspecialchars($name); ?></strong><br>
                        <small>Code: <?php echo htmlspecialchars($code); ?> | Unit: <?php echo htmlspecialchars($unitMapping[$code]); ?></small>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- API URL Display -->
        <?php if (!empty($apiUrl)): ?>
            <div class="debug-info">
                <h6>Generated API URL:</h6>
                <div class="api-url-display">
                    <code><?php echo htmlspecialchars($apiUrl); ?></code>
                </div>
                <small class="text-muted">You can test this URL directly in your browser</small>
            </div>
        <?php endif; ?>

        <!-- Search Form -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">Search Climate Data</h3>
            </div>
            <div class="card-body">
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php endif; ?>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-success"><?php echo $message; ?></div>
                <?php endif; ?>

                <form method="post" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                    <div class="mb-3">
                        <label for="qldStations" class="form-label">Select a Queensland Station:</label>
                        <select id="qldStations" class="form-select">
                            <option value="">-- Select Station --</option>
                        </select>
                        <div class="station-details mt-2 p-2 bg-light rounded" style="display: none;">
                            <small>
                                <strong>Selected Station:</strong> <span id="selectedStationName">N/A</span><br>
                                <strong>Coordinates:</strong> <span id="selectedStationCoords">N/A</span>
                            </small>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="latitude" class="form-label">Latitude (-29.0 to -10.0)</label>
                            <input type="number" id="latitude" name="latitude" class="form-control"
                                step="0.01" min="-29.0" max="-10.0" required
                                value="<?php echo isset($_POST['latitude']) ? htmlspecialchars($_POST['latitude']) : '-27.47'; ?>">
                        </div>
                        <div class="col-md-6">
                            <label for="longitude" class="form-label">Longitude (138.0 to 155.0)</label>
                            <input type="number" id="longitude" name="longitude" class="form-control"
                                step="0.01" min="138.0" max="155.0" required
                                value="<?php echo isset($_POST['longitude']) ? htmlspecialchars($_POST['longitude']) : '153.03'; ?>">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" id="start_date" name="start_date" class="form-control" required
                                value="<?php echo isset($_POST['start_date']) ? htmlspecialchars($_POST['start_date']) : date('Y-m-d', strtotime('-1 month')); ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" id="end_date" name="end_date" class="form-control" required
                                value="<?php echo isset($_POST['end_date']) ? htmlspecialchars($_POST['end_date']) : date('Y-m-d'); ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="variable" class="form-label">Climate Variable</label>
                            <select id="variable" name="variable" class="form-select" required>
                                <option value="">-- Select Variable --</option>
                                <?php foreach ($variableMapping as $key => $name): ?>
                                    <option value="<?php echo htmlspecialchars($key); ?>"
                                        <?php echo (isset($_POST['variable']) && $_POST['variable'] === $key) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($variableNames[$key]); ?> (<?php echo htmlspecialchars($unitMapping[$key]); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <button type="submit" name="submit" class="btn btn-primary">Search & Save Data</button>
                </form>
            </div>
        </div>

        <!-- Results Display -->
        <?php if ($searchPerformed && !empty($climateData)): ?>
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">Search Results</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Variable</th>
                                    <th>Value</th>
                                    <th>Unit</th>
                                    <th>Source</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($climateData as $date => $data): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($date); ?></td>
                                        <td><?php echo htmlspecialchars($variableNames[$_POST['variable']] ?? 'Unknown'); ?></td>
                                        <td><?php echo htmlspecialchars($data['value']); ?></td>
                                        <td><?php echo htmlspecialchars($data['unit']); ?></td>
                                        <td><?php echo htmlspecialchars($data['source']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Saved Data Display -->
        <?php if (!empty($savedData)): ?>
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">Recently Saved Data (Last 100 records)</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Location</th>
                                    <th>Date</th>
                                    <th>Variable</th>
                                    <th>Value</th>
                                    <th>Unit</th>
                                    <th>Source</th>
                                    <th>Saved At</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($savedData as $row): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($row['latitude'] . ', ' . $row['longitude']); ?></td>
                                        <td><?php echo htmlspecialchars($row['date']); ?></td>
                                        <td><?php echo htmlspecialchars($row['variable_name']); ?></td>
                                        <td><?php echo htmlspecialchars($row['value']); ?></td>
                                        <td><?php echo htmlspecialchars($row['unit']); ?></td>
                                        <td><?php echo htmlspecialchars($row['source']); ?></td>
                                        <td><?php echo htmlspecialchars(date('Y-m-d H:i', strtotime($row['created_at']))); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Queensland weather stations data
        const qldStations = [
            { name: "Brisbane", lat: -27.47, lon: 153.03 },
            { name: "Gold Coast", lat: -28.02, lon: 153.43 },
            { name: "Sunshine Coast", lat: -26.65, lon: 153.07 },
            { name: "Toowoomba", lat: -27.56, lon: 151.95 },
            { name: "Cairns", lat: -16.92, lon: 145.77 },
            { name: "Townsville", lat: -19.25, lon: 146.82 },
            { name: "Mackay", lat: -21.15, lon: 149.18 },
            { name: "Rockhampton", lat: -23.38, lon: 150.51 },
            { name: "Bundaberg", lat: -24.87, lon: 152.35 },
            { name: "Hervey Bay", lat: -25.29, lon: 152.85 },
            { name: "Gladstone", lat: -23.84, lon: 151.26 },
            { name: "Mount Isa", lat: -20.73, lon: 139.50 },
            { name: "Charleville", lat: -26.41, lon: 146.26 },
            { name: "Roma", lat: -26.57, lon: 148.79 },
            { name: "Emerald", lat: -23.53, lon: 148.16 },
            { name: "Longreach", lat: -23.44, lon: 144.25 },
            { name: "Weipa", lat: -12.68, lon: 141.88 },
            { name: "Thursday Island", lat: -10.58, lon: 142.22 },
            { name: "Charters Towers", lat: -20.07, lon: 146.27 },
            { name: "Ipswich", lat: -27.61, lon: 152.76 }
        ];

        // Populate the stations dropdown
        document.addEventListener('DOMContentLoaded', function() {
            const stationSelect = document.getElementById('qldStations');
            const stationDetails = document.querySelector('.station-details');
            const stationName = document.getElementById('selectedStationName');
            const stationCoords = document.getElementById('selectedStationCoords');
            const latInput = document.getElementById('latitude');
            const lonInput = document.getElementById('longitude');

            // Sort stations alphabetically
            qldStations.sort((a, b) => a.name.localeCompare(b.name));

            // Add stations to dropdown
            qldStations.forEach(station => {
                const option = document.createElement('option');
                option.value = `${station.lat},${station.lon}`;
                option.textContent = `${station.name} (${station.lat}, ${station.lon})`;
                stationSelect.appendChild(option);
            });

            // Handle station selection
            stationSelect.addEventListener('change', function() {
                if (this.value) {
                    const [lat, lon] = this.value.split(',');
                    const selectedStation = qldStations.find(s => 
                        s.lat.toString() === lat && s.lon.toString() === lon
                    );

                    if (selectedStation) {
                        latInput.value = lat;
                        lonInput.value = lon;
                        stationName.textContent = selectedStation.name;
                        stationCoords.textContent = `${lat}, ${lon}`;
                        stationDetails.style.display = 'block';
                    }
                } else {
                    stationDetails.style.display = 'none';
                }
            });

            // Form validation
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                const lat = parseFloat(latInput.value);
                const lon = parseFloat(lonInput.value);
                const startDate = document.getElementById('start_date').value;
                const endDate = document.getElementById('end_date').value;

                // Validate coordinates
                if (lat < -29.0 || lat > -10.0) {
                    alert('Latitude must be between -29.0 and -10.0 for Queensland');
                    e.preventDefault();
                    return;
                }

                if (lon < 138.0 || lon > 155.0) {
                    alert('Longitude must be between 138.0 and 155.0 for Queensland');
                    e.preventDefault();
                    return;
                }

                // Validate date range
                if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
                    alert('Start date must be before end date');
                    e.preventDefault();
                    return;
                }

                // Check date range isn't too large (more than 1 year)
                if (startDate && endDate) {
                    const start = new Date(startDate);
                    const end = new Date(endDate);
                    const diffTime = Math.abs(end - start);
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    
                    if (diffDays > 365) {
                        if (!confirm('You are requesting more than 1 year of data. This may take a long time. Continue?')) {
                            e.preventDefault();
                            return;
                        }
                    }
                }
            });

            // Auto-update coordinates when manually entered
            [latInput, lonInput].forEach(input => {
                input.addEventListener('change', function() {
                    // Check if the coordinates match any station
                    const lat = parseFloat(latInput.value);
                    const lon = parseFloat(lonInput.value);
                    
                    const matchingStation = qldStations.find(station => 
                        Math.abs(station.lat - lat) < 0.01 && 
                        Math.abs(station.lon - lon) < 0.01
                    );

                    if (matchingStation) {
                        stationSelect.value = `${matchingStation.lat},${matchingStation.lon}`;
                        stationName.textContent = matchingStation.name;
                        stationCoords.textContent = `${matchingStation.lat}, ${matchingStation.lon}`;
                        stationDetails.style.display = 'block';
                    } else {
                        stationSelect.value = '';
                        stationDetails.style.display = 'none';
                    }
                });
            });
        });

        // Add some helpful tooltips
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Bootstrap tooltips if available
            if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
                const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            }
        });
    </script>
</body>
</html>