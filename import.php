<?php
// SILO Climate Data Application
// A proof of concept for Digital Solutions IA3
// This application connects to the Queensland SILO Climate API
// and allows users to search and view climate information

// Start session for security purposes
session_start();

// Generate CSRF token for form security
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Configuration
$apiBaseUrl = "https://www.longpaddock.qld.gov.au/cgi-bin/silo/";

/**
 * Function to fetch data from SILO API
 * @param string $endpoint API endpoint
 * @param array $params Query parameters
 * @return array Decoded JSON response
 */
function fetchFromAPI($endpoint, $params) {
    global $apiBaseUrl;
    
    // Build query URL
    $url = $apiBaseUrl . $endpoint . "?" . http_build_query($params);
    
    // Initialize cURL session
    $ch = curl_init();
    
    // Set cURL options
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    // Execute cURL request
    $response = curl_exec($ch);
    
    // Check for errors
    if (curl_errno($ch)) {
        return ['error' => 'API Error: ' . curl_error($ch)];
    }
    
    // Close cURL session
    curl_close($ch);
    
    // Parse response
    $data = json_decode($response, true);
    
    // Check if parsing was successful
    if (json_last_error() !== JSON_ERROR_NONE) {
        return ['error' => 'Failed to parse API response'];
    }
    
    return $data;
}

/**
 * Transform API data to a more readable format
 * @param array $data Raw API data
 * @return array Transformed data
 */
function transformData($data) {
    $transformed = [];
    
    // Check if data is available
    if (empty($data) || isset($data['error'])) {
        return $data;
    }
    
    // Transform data structure (specific implementation depends on API response format)
    // This is a placeholder - actual transformation will depend on the SILO API response structure
    foreach ($data as $key => $value) {
        if (is_array($value)) {
            // Format dates and values
            if (isset($value['date']) && isset($value['value'])) {
                $date = date('Y-m-d', strtotime($value['date']));
                $transformed[$date] = [
                    'value' => round($value['value'], 2),
                    'unit' => $value['unit'] ?? ''
                ];
            }
        }
    }
    
    return $transformed;
}

// Initialize variables
$climateData = [];
$error = '';
$searchPerformed = false;

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit'])) {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $error = 'Security validation failed. Please try again.';
    } else {
        // Sanitize and validate inputs
        $latitude = filter_input(INPUT_POST, 'latitude', FILTER_VALIDATE_FLOAT);
        $longitude = filter_input(INPUT_POST, 'longitude', FILTER_VALIDATE_FLOAT);
        $startDate = filter_input(INPUT_POST, 'start_date', FILTER_SANITIZE_STRING);
        $endDate = filter_input(INPUT_POST, 'end_date', FILTER_SANITIZE_STRING);
        $variable = filter_input(INPUT_POST, 'variable', FILTER_SANITIZE_STRING);
        
        // Validate inputs
        if ($latitude === false || $longitude === false || empty($startDate) || empty($endDate) || empty($variable)) {
            $error = 'Please provide valid inputs for all fields.';
        } else {
            // Prepare API parameters
            $params = [
                'lat' => $latitude,
                'lon' => $longitude,
                'start' => $startDate,
                'end' => $endDate,
                'variable' => $variable,
                'format' => 'json'
            ];
            
            // Fetch data from API
            $response = fetchFromAPI('PatchedPoint', $params);
            
            // Check for errors
            if (isset($response['error'])) {
                $error = $response['error'];
            } else {
                // Transform data
                $climateData = transformData($response);
                $searchPerformed = true;
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Queensland Climate Data Explorer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        h1, h2 {
            color: #005A87;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .card {
            background: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        button {
            background: #005A87;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background: #004A77;
        }
        
        .error {
            color: #d9534f;
            padding: 10px;
            background-color: #f9f2f2;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        tr:hover {
            background-color: #f5f5f5;
        }
        
        .responsive {
            overflow-x: auto;
        }
        
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Queensland Climate Data Explorer</h1>
        
        <div class="card">
            <h2>Search Climate Data</h2>
            
            <?php if (!empty($error)): ?>
                <div class="error"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>
            
            <form method="post" action="">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="latitude">Latitude:</label>
                        <input type="number" id="latitude" name="latitude" step="0.000001" required 
                               value="<?php echo isset($_POST['latitude']) ? htmlspecialchars($_POST['latitude']) : '-27.4698'; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="longitude">Longitude:</label>
                        <input type="number" id="longitude" name="longitude" step="0.000001" required 
                               value="<?php echo isset($_POST['longitude']) ? htmlspecialchars($_POST['longitude']) : '153.0251'; ?>">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="start_date">Start Date:</label>
                        <input type="date" id="start_date" name="start_date" required 
                               value="<?php echo isset($_POST['start_date']) ? htmlspecialchars($_POST['start_date']) : '2024-01-01'; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="end_date">End Date:</label>
                        <input type="date" id="end_date" name="end_date" required 
                               value="<?php echo isset($_POST['end_date']) ? htmlspecialchars($_POST['end_date']) : '2024-01-31'; ?>">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="variable">Climate Variable:</label>
                    <select id="variable" name="variable" required>
                        <option value="daily_rain" <?php echo (isset($_POST['variable']) && $_POST['variable'] === 'daily_rain') ? 'selected' : ''; ?>>Daily Rainfall</option>
                        <option value="max_temp" <?php echo (isset($_POST['variable']) && $_POST['variable'] === 'max_temp') ? 'selected' : ''; ?>>Maximum Temperature</option>
                        <option value="min_temp" <?php echo (isset($_POST['variable']) && $_POST['variable'] === 'min_temp') ? 'selected' : ''; ?>>Minimum Temperature</option>
                        <option value="daily_solar" <?php echo (isset($_POST['variable']) && $_POST['variable'] === 'daily_solar') ? 'selected' : ''; ?>>Solar Radiation</option>
                        <option value="vp" <?php echo (isset($_POST['variable']) && $_POST['variable'] === 'vp') ? 'selected' : ''; ?>>Vapor Pressure</option>
                        <option value="vp_deficit" <?php echo (isset($_POST['variable']) && $_POST['variable'] === 'vp_deficit') ? 'selected' : ''; ?>>Vapor Pressure Deficit</option>
                    </select>
                </div>
                
                <button type="submit" name="submit">Search</button>
            </form>
        </div>
        
        <?php if ($searchPerformed): ?>
            <div class="card">
                <h2>Climate Data Results</h2>
                
                <?php if (empty($climateData)): ?>
                    <p>No data available for the selected criteria.</p>
                <?php else: ?>
                    <div class="responsive">
                        <table>
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Value</th>
                                    <th>Unit</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($climateData as $date => $data): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($date); ?></td>
                                        <td><?php echo htmlspecialchars($data['value']); ?></td>
                                        <td><?php echo htmlspecialchars($data['unit']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>