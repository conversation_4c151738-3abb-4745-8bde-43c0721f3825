-- Create database if not exists
CREATE DATABASE IF NOT EXISTS climate_data;
USE climate_data;

-- Create locations table
CREATE TABLE IF NOT EXISTS locations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    latitude DECIMAL(10, 6) NOT NULL,
    longitude DECIMAL(10, 6) NOT NULL,
    elevation DECIMAL(10, 2),
    reference VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY lat_lon_unique (latitude, longitude)
);

-- Create rainfall data table
CREATE TABLE IF NOT EXISTS rainfall_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    location_id INT NOT NULL,
    date DATE NOT NULL,
    value DECIMAL(10, 2) NOT NULL,
    source INT,
    variable_code VARCHAR(20) DEFAULT 'daily_rain',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY location_date_unique (location_id, date),
    FOREIGN KEY (location_id) REFERENCES locations(id)
);