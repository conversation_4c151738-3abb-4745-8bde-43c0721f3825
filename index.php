<?php

/**
 * Main application entry point
 */
session_start();
require_once 'auth.php';
require_once 'database.php';

$auth = new Auth();

// Check if user is logged in
$isLoggedIn = $auth->isLoggedIn();
$currentUser = $isLoggedIn ? $auth->getCurrentUser() : null;

// Generate CSRF token for forms
$csrfToken = $auth->generateCSRFToken();
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Queensland Climate Data Explorer</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Climate Data Management System</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css" rel="stylesheet">
    <style>
        .sidebar {
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding-top: 20px;
            transition: all 0.3s;
        }

        .main-content {
            margin-left: 250px;
            padding: 20px;
            min-height: 100vh;
            background-color: #f8f9fa;
        }

        .nav-link {
            color: white !important;
            padding: 15px 20px;
            border-radius: 8px;
            margin: 5px 15px;
            transition: all 0.3s;
        }

        .nav-link:hover,
        .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateX(5px);
        }

        .card {
            border: none;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            border-radius: 15px;
            margin-bottom: 20px;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .data-table {
            max-height: 400px;
            overflow-y: auto;
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }

        .hidden {
            display: none;
        }

        .loading {
            text-align: center;
            padding: 20px;
        }

        .spinner-border {
            color: #667eea;
        }
    </style>
</head>

<body>
    <header>
        <div class="container">
            <h1>Queensland Climate Data Explorer</h1>
            <nav>
                <ul>
                    <li><a href="index.php">Home</a></li>
                    <li><a href="import.php">Import Data</a></li>
                    <li><a href="explore.php">Explore Data</a></li>
                    <?php if ($isLoggedIn && $auth->hasPermission('manage_users')): ?>
                        <li><a href="users.php">Manage Users</a></li>
                    <?php endif; ?>
                    <?php if ($isLoggedIn): ?>
                        <li><a href="logout.php">Logout (<?php echo htmlspecialchars($currentUser['username']); ?>)</a></li>
                    <?php else: ?>
                        <li><a href="login.php">Login</a></li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    </header>

    <main class="container">
        <section class="welcome">
            <h2>Welcome to the Queensland Climate Data Explorer</h2>
            <p>This application allows you to access, analyze, and visualize climate data from Queensland, Australia.</p>

            <?php if (!$isLoggedIn): ?>
                <div class="cta">
                    <p>Please <a href="login.php">login</a> to access all features.</p>
                </div>
            <?php else: ?>
                <div class="dashboard">
                    <h3>Your Dashboard</h3>
                    <div class="dashboard-stats" id="dashboard-stats">
                        <p>Loading statistics...</p>
                    </div>
                </div>
            <?php endif; ?>
        </section>

        <section class="features">
            <h2>Features</h2>
            <div class="feature-grid">
                <div class="feature">
                    <h3>Import Data</h3>
                    <p>Import climate data from the SILO API for any location in Queensland.</p>
                </div>
                <div class="feature">
                    <h3>Explore Data</h3>
                    <p>View and analyze climate data with interactive charts and tables.</p>
                </div>
                <div class="feature">
                    <h3>Export Results</h3>
                    <p>Export your data and analysis results in various formats.</p>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Queensland Climate Data Explorer</p>
        </div>
    </footer>

    <?php if ($isLoggedIn): ?>
        <script>
            // Fetch dashboard data
            fetch('/api/dashboard')
                .then(response => response.json())
                .then(data => {
                    const dashboardStats = document.getElementById('dashboard-stats');
                    dashboardStats.innerHTML = `
                    <div class="stat-grid">
                        <div class="stat">
                            <h4>Total Records</h4>
                            <p>${data.total_records}</p>
                        </div>
                        <div class="stat">
                            <h4>Locations</h4>
                            <p>${data.locations}</p>
                        </div>
                    </div>
                    <h4>Recent Imports</h4>
                    <ul class="recent-imports">
                        ${data.recent_imports.map(imp => `
                            <li>
                                ${imp.records_count} records imported on 
                                ${new Date(imp.created_at).toLocaleDateString()} by 
                                ${imp.username}
                            </li>
                        `).join('')}
                    </ul>
                `;
                })
                .catch(error => {
                    console.error('Error fetching dashboard data:', error);
                });
        </script>
    <?php endif; ?>
</body>

</html>