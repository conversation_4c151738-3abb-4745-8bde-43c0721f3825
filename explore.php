<?php
/**
 * Data exploration page
 */
require_once 'auth.php';
require_once 'database.php';

$auth = new Auth();

// Redirect if not logged in
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// Generate CSRF token
$csrfToken = $auth->generateCSRFToken();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Explore Data - Queensland Climate Data Explorer</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <header>
        <div class="container">
            <h1>Queensland Climate Data Explorer</h1>
            <nav>
                <ul>
                    <li><a href="index.php">Home</a></li>
                    <li><a href="import.php">Import Data</a></li>
                    <li><a href="explore.php" class="active">Explore Data</a></li>
                    <?php if ($auth->hasPermission('manage_users')): ?>
                    <li><a href="users.php">Manage Users</a></li>
                    <?php endif; ?>
                    <li><a href="logout.php">Logout (<?php echo htmlspecialchars($auth->getCurrentUser()['username']); ?>)</a></li>
                </ul>
            </nav>
        </div>
    </header>
    
    <main class="container">
        <h2>Explore Climate Data</h2>
        
        <div class="card">
            <h3>Filter Data</h3>
            <form id="filter-form">
                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="start_date">Start Date:</label>
                        <input type="date" id="start_date" name="start_date">
                    </div>
                    
                    <div class="form-group">
                        <label for="end_date">End Date:</label>
                        <input type="date" id="end_date" name="end_date">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="location">Location:</label>
                    <input type="text" id="location" name="location" placeholder="Enter location name">
                </div>
                
                <div class="form-group">
                    <label for="chart_type">Chart Type:</label>
                    <select id="chart_type" name="chart_type">
                        <option value="line">Line Chart</option>
                        <option value="bar">Bar Chart</option>
                    </select>
                </div>
                
                <button type="submit">Apply Filters</button>
            </form>
        </div>
        
        <div class="card">
            <h3>Data Visualization</h3>
            <div class="chart-container">
                <canvas id="climate-chart"></canvas>
            </div>
        </div>
        
        <div class="card">
            <h3>Data Table</h3>
            <div class="responsive">
                <table id="data-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Location</th>
                            <th>Temperature</th>
                            <th>Rainfall</th>
                            <th>Humidity</th>
                            <th>Pressure</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="6">Apply filters to load data</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </main>
    
    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Queensland Climate Data Explorer</p>
        </div>
    </footer>
    
    <script>
        // Initialize chart
        let climateChart = null;
        
        // Handle form submission
        document.getElementById('filter-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const params = new URLSearchParams();
            
            for (const [key, value] of formData.entries()) {
                if (value) {
                    params.append(key, value);
                }
            }
            
            // Fetch data from API
            fetch(`/api/climate-data?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    updateChart(data);
                    updateTable(data);
                })
                .catch(error => {
                    console.error('Error fetching data:', error);
                });
        });
        
        function updateChart(data) {
            const chartType = document.getElementById('chart_type').value;
            const dates = data.map(item => item.date);
            const temperatures = data.map(item => parseFloat(item.temperature));
            const rainfall = data.map(item => parseFloat(item.rainfall));
            
            const ctx = document.getElementById('climate-chart').getContext('2d');
            
            if (climateChart) {
                climateChart.destroy();
            }
            
            climateChart = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: dates,
                    datasets: [
                        {
                            label: 'Temperature (°C)',
                            data: temperatures,
                            borderColor: 'rgb(255, 99, 132)',
                            backgroundColor: 'rgba(255, 99, 132, 0.2)',
                            yAxisID: 'y'
                        },
                        {
                            label: 'Rainfall (mm)',
                            data: rainfall,
                            borderColor: 'rgb(54, 162, 235)',
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Temperature (°C)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Rainfall (mm)'
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    }
                }
            });
        }
        
        function updateTable(data) {
            const tableBody = document.querySelector('#data-table tbody');
            
            if (data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="6">No data available</td></tr>';
                return;
            }
            
            let html = '';
            
            data.forEach(item => {
                html += `
                    <tr>
                        <td>${item.date}</td>
                        <td>${item.location}</td>
                        <td>${parseFloat(item.temperature).toFixed(1)} °C</td>
                        <td>${parseFloat(item.rainfall).toFixed(1)} mm</td>
                        <td>${item.humidity}%</td>
                        <td>${item.pressure} hPa</td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
        }
    </script>
</body>
</html>