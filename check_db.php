<?php
require_once 'database.php';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Check rainfall_data table structure
    $stmt = $conn->query('DESCRIBE rainfall_data');
    echo "RAINFALL_DATA TABLE STRUCTURE:\n";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo $row['Field'] . " - " . $row['Type'] . " - " . ($row['Null'] === 'YES' ? 'NULL' : 'NOT NULL') . "\n";
    }
    
    // Check if variable_code column exists
    $stmt = $conn->query("SELECT COUNT(*) as count FROM information_schema.columns 
                         WHERE table_schema = 'climate_data' 
                         AND table_name = 'rainfall_data' 
                         AND column_name = 'variable_code'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "\nVariable_code column exists: " . ($result['count'] > 0 ? 'YES' : 'NO') . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}