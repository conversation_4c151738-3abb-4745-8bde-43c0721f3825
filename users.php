<?php
/**
 * User management page
 */
require_once 'auth.php';
require_once 'database.php';

$auth = new Auth();

// Redirect if not logged in or doesn't have permission
if (!$auth->isLoggedIn() || !$auth->hasPermission('manage_users')) {
    header('Location: index.php');
    exit;
}

// Generate CSRF token
$csrfToken = $auth->generateCSRFToken();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Users - Queensland Climate Data Explorer</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <header>
        <div class="container">
            <h1>Queensland Climate Data Explorer</h1>
            <nav>
                <ul>
                    <li><a href="index.php">Home</a></li>
                    <li><a href="import.php">Import Data</a></li>
                    <li><a href="explore.php">Explore Data</a></li>
                    <li><a href="users.php" class="active">Manage Users</a></li>
                    <li><a href="logout.php">Logout (<?php echo htmlspecialchars($auth->getCurrentUser()['username']); ?>)</a></li>
                </ul>
            </nav>
        </div>
    </header>
    
    <main class="container">
        <h2>Manage Users</h2>
        
        <div class="card">
            <h3>Add New User</h3>
            <div id="add-user-message" class="message"></div>
            
            <form id="add-user-form">
                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="username">Username:</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="name">Full Name:</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="password">Password:</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="role">Role:</label>
                        <select id="role" name="role" required>
                            <option value="admin">Administrator</option>
                            <option value="researcher">Researcher</option>
                            <option value="viewer">Viewer</option>
                        </select>
                    </div>
                </div>
                
                <button type="submit">Add User</button>
            </form>
        </div>
        
        <div class="card">
            <h3>User List</h3>
            <div class="responsive">
                <table id="users-table">
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Name</th>
                            <th>Role</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="5">Loading users...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </main>
    
    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Queensland Climate Data Explorer</p>
        </div>
    </footer>
    
    <script>
        // Load users on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadUsers();
            
            // Handle form submission
            document.getElementById('add-user-form').addEventListener('submit', function(e) {
                e.preventDefault();
                addUser();
            });
        });
        
        function loadUsers() {
            fetch('/api/users')
                .then(response => response.json())
                .then(users => {
                    const tableBody = document.querySelector('#users-table tbody');
                    
                    if (users.length === 0) {
                        tableBody.innerHTML = '<tr><td colspan="5">No users found</td></tr>';
                        return;
                    }
                    
                    let html = '';
                    
                    users.forEach(user => {
                        html += `
                            <tr>
                                <td>${user.username}</td>
                                <td>${user.name}</td>
                                <td>${user.role}</td>
                                <td>${new Date(user.created_at).toLocaleDateString()}</td>
                                <td>
                                    <button class="btn-small" onclick="editUser(${user.id})">Edit</button>
                                </td>
                            </tr>
                        `;
                    });
                    
                    tableBody.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading users:', error);
                });
        }
        
        function addUser() {
            const form = document.getElementById('add-user-form');
            const formData = new FormData(form);
            const userData = {};
            
            for (const [key, value] of formData.entries()) {
                userData[key] = value;
            }
            
            fetch('/api/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            })
            .then(response => response.json())
            .then(data => {
                const messageDiv = document.getElementById('add-user-message');
                
                if (data.success) {
                    messageDiv.className = 'message success';
                    messageDiv.textContent = 'User added successfully';
                    form.reset();
                    loadUsers();
                } else {
                    messageDiv.className = 'message error';
                    messageDiv.textContent = data.error || 'Failed to add user';
                }
            })
            .catch(error => {
                console.error('Error adding user:', error);
                const messageDiv = document.getElementById('add-user-message');
                messageDiv.className = 'message error';
                messageDiv.textContent = 'An error occurred while adding the user';
            });
        }
        
        function editUser(userId) {
            // This would open a modal or redirect to an edit page
            alert('Edit user functionality would be implemented here');
        }
    </script>
</body>
</html>